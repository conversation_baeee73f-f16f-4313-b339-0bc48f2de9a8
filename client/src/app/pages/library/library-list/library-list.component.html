<dialog class="stylized_wide" #modal>
  <div (click)="closeModal(modal)" class="x_bt"></div>
  <div class="flex flex-col cont_mod">
    <p class="pr_20 text-center">Фильтры</p>
        <div class="format-options">
          <div class="checkbox-container" *ngFor="let option of formatOptions">
            <input class="checkbox_" type="checkbox" [id]="'format-' + option.id" [value]="option.value"
              [checked]="filters.audio" (change)="changeAudioFilter($event)">
            <span [id]="'format-' + option.id" (click)="changeAudioFilter($event)"></span>
            <label [for]="'format-' + option.id">{{ option.name }}</label>
          </div>

          <div class="checkbox-container">
            <input id="format-paid" class="checkbox_" type="checkbox" value="paid"
                   [checked]="filters.paid" (change)="changePaidFilter($event)">
            <span id="format-paid" (click)="changeAudioFilter($event)"></span>
            <label [for]="'format-paid'">Платные книги</label>
          </div>
        </div>
    <p class="auth_head a_mg_modal">
      Выбор автора
    </p>
    <div class="catg_wrap">
      <app-custom-dropdown placeholderText="Выбор автора" [type]="'multiselect'" [options]="libraryService.list().authors" [title]="'name'" [selected]="selectedAuthors"
        (selectedChange)="changeAuthorFilter($event)">
      </app-custom-dropdown>
    </div>
    <p class="auth_head a_mg_modal">
      Выбор тегов
    </p>
    @if(libraryService.list().audioTags) {
    <app-custom-dropdown [type]="'multiselect'" [options]="libraryService.list().audioTags" [selected]="selectedTags"
      (selectedChange)="changeTagFilter($event)" class="a">
    </app-custom-dropdown>
    }

    <p class="auth_head a_mg_modal">
      Выбор категории
    </p>
    @if(libraryService.list().categories) {
      <app-custom-dropdown placeholderText="Выбор категории" [type]="'multiselect'" [options]="libraryService.list().categories" [selected]="selectedCategories"
                           (selectedChange)="changeCategoryFilter($event)" class="a">
      </app-custom-dropdown>
    }

    <div class="filter-buttons-container flex justify-between mt-4">
      <button class="save-btn" (click)="resetFilters(); closeModal(modal)">
        <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="218" height="54" alt="bg">
        <div class="save-btn-label">Сбросить все</div>
      </button>
      <button class="save-btn" (click)="closeModal(modal)">
        <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="218" height="54" alt="bg">
        <div class="save-btn-label">Показать</div>
      </button>
    </div>
  </div>
</dialog>
<div>
  <div class="middle_stripe">
    <breadcrumb></breadcrumb>
    <div class="wrapper_line">
      <div class="dec_head _background">
        <img class="carn_b_" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
        <h1 class="dec_head-title_">Книги</h1>
        <img class="carn_b_ rotate" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
      </div>
      <div class="cat_wrap">
        <form [formGroup]="filter">
          <div class="articles-search relative">
            <input formControlName="search" type="text" placeholder="Поиск" (input)="searchSubject.next($event)">
            <div (click)="openModal()" class="p_filter">
              <span>Фильтр</span>
            </div>

            <div class="articles-sort_ custom-dropdown" (click)="toggleSortDropdown()">
              @if (dropdownSortOpen) {
                <div class="dropdown-content">
                  @for(option of sortOptions; track option.id) {
                    <div
                      (click)="selectSort(option.value)"
                      class="dropdown-item cat_i"
                      [class.active]="currentSortField === option.value">
                      {{ option.label }}
                      <span class="sort-arrow" *ngIf="currentSortField === option.value">
                      <svg [ngClass]="{'rotate-down': sortDirection === 'Desc', 'rotate-up': sortDirection === 'Asc'}" width="30" height="30" viewBox="0 0 24 24"><path d="M7 10l5 5 5-5H7z"/></svg>
                    </span>
                    </div>
                  }
                </div>
              }
            </div>
          </div>
          <!-- <div class="flex articles-tag">
          <div class="tags_wrap">
            @for(tag of selectedTags; track tag.id) {
            <p class="tag_item_wrapper">
              <span class="tag_item_">{{tag.name}}
                <span class="x_a" (click)="removeTag(tag)"></span>
              </span>
            </p>
            }
          </div>
          <div class="articles-sort custom-dropdown" (click)="toggleDropdown()">
            <button class="dropdown-btn"></button>
            @if (dropdownOpen) {
            <div class="dropdown-content">
              @for(option of sortOptions; track option.id) {
              <div (click)="selectSort(option.value)" class="dropdown-item"
                [class.active]="filter.get('sort')?.value === option.value">
                {{ option.label }}
              </div>
              }
            </div>
            }
          </div>
        </div> -->
        </form>
        <div class="itm_a_wrap">
          @for(item of data; track item.id; let i = $index; let last = $last) {
          <div [ngClass]="{'widen': isOpened[i], 'last': last}" class="article-item relative">
            <div class="vis_part">
              <div (click)="isOpened[i] = !isOpened[i]" class="art_img_"></div>
              <div class="art_img">
                <img [routerLink]="['/ru/library/' + item.code]" [src]="getLangElement(item).image" alt="img">
              </div>
              <div (click)="isOpened[i] = !isOpened[i]" class="flex justify-between">
                <div class="titl_w">
                  <a class="article-title" [routerLink]="['/ru/library/' + item.code]">
                    {{getLangElement(item).title}}
                  </a>
                  <div class="article-category">
                    <span [ngClass]="{'opacity-0': !getLangElement(item).author}">Автор: {{getLangElement(item).author}}</span>&nbsp;&nbsp;<span [ngClass]="{'opacity-0': !getLangElement(item).reader}">Чтец: {{getLangElement(item).reader}}</span></div>
                  <div class="flex items-center icons_w b_p">
                    <div class=" flex items-center">
                      <div class="mr-2 chckk"></div>
                      <span class="ml-1 text-color">
                        {{getLangElement(item).views}} просмотров
                      </span>
                    </div>
                  </div>
                </div>
                <div class="actions_w" (click)="showMenu(i, $event)"
                    [class.show_menu]="show_menu[i]">
                  <button *ngIf="hasAudio(item)" class="flex items-center icons_w lik_hov show_md separatre_" (click)="navigateToAudiobook(item, $event)">
                    <div class="icon-wrap like_w no_h">
                      <svg class="emty_l" width="21" height="22" viewBox="0 0 21 22" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M19.4948 12.1068C17.2112 10.6027 14.8396 10.3315 12.4366 11.589C10.0398 12.8404 8.93264 14.9055 8.90748 17.5685C8.81941 17.5685 8.74392 17.5685 8.66843 17.5685C7.42916 17.5685 6.18989 17.5685 4.95061 17.5685C4.36558 17.5685 4.12024 17.3342 4.12024 16.7548C4.12024 11.4411 4.12024 6.12739 4.12024 0.813697C4.11395 0.234246 4.35299 0 4.94432 0C8.97039 0 12.9964 0 17.0225 0C18.4945 0 19.4948 0.9863 19.4948 2.4226C19.4948 5.57259 19.4948 8.72875 19.4948 11.8787C19.4948 11.9466 19.4948 12.0144 19.4948 12.1068ZM11.8201 6.20136C12.9021 6.20136 13.9841 6.20136 15.0661 6.20136C15.5316 6.20136 15.8399 5.94862 15.8461 5.5541C15.8524 5.16574 15.5442 4.87602 15.0913 4.87602C12.9021 4.86985 10.7129 4.87602 8.52374 4.87602C8.39164 4.87602 8.25324 4.90684 8.14001 4.95616C7.85693 5.08561 7.71224 5.39999 7.78773 5.68972C7.86322 5.99177 8.12114 6.20136 8.45455 6.20136C9.5743 6.20136 10.694 6.20136 11.8201 6.20136ZM11.8201 8.96916C12.3359 8.96916 12.8518 8.96916 13.3739 8.96916C13.808 8.96916 14.1225 8.69177 14.1225 8.30341C14.1225 7.92122 13.8142 7.63766 13.3739 7.63766C12.3296 7.63149 11.2917 7.63149 10.2474 7.63766C9.80076 7.63766 9.51139 7.91506 9.51139 8.30341C9.51139 8.69177 9.81334 8.963 10.2537 8.963C10.7695 8.97533 11.2917 8.96916 11.8201 8.96916Z"
                          fill="var(--font-color1)" />
                        <path
                          d="M3.04471 0.0124731C3.02584 0.406993 2.98809 0.789184 2.98809 1.17138C2.9818 6.27548 2.9818 11.3857 2.98809 16.4898C2.98809 16.8412 3.03213 17.1926 3.051 17.5563C2.99438 17.5624 2.89373 17.5748 2.79308 17.5809C1.90609 17.6549 1.39025 18.407 1.36509 19.1837C1.33992 19.9172 1.98787 20.5707 2.7805 20.657C2.90631 20.6693 3.03213 20.6755 3.15794 20.6755C4.98854 20.6755 6.81914 20.6755 8.64345 20.6755C8.73782 20.6755 8.83218 20.6755 8.87621 20.6755C9.05235 21.1316 9.21591 21.557 9.37947 21.9885C9.33543 21.9885 9.26624 21.9946 9.19704 21.9946C7.13368 21.9946 5.07032 22.0008 3.00068 21.9946C1.51606 21.9885 0.333408 21.0576 0.0566165 19.6645C-0.00629073 19.3563 0.00629072 19.0357 0.00629072 18.7213C0.00629072 13.4816 0.00629072 8.24808 0 3.00836C0 1.86179 0.478095 0.974115 1.48461 0.376171C1.98158 0.0987743 2.51629 -0.0306776 3.04471 0.0124731Z"
                          fill="var(--font-color1)" />
                        <path
                          d="M19.6212 17.1801C19.6149 14.9363 17.5956 13.1178 15.2303 13.2719C12.6826 13.4383 11.3678 15.6452 11.4433 17.1739C11.8711 17.1739 12.3051 17.1739 12.7329 17.1739C13.7205 17.1801 14.4062 17.8397 14.4251 18.8013C14.4314 19.3253 14.4377 19.8493 14.4251 20.3733C14.4062 21.3164 13.7331 21.976 12.7706 21.9945C12.3554 22.0007 11.9403 22.0068 11.5251 21.9822C10.8016 21.939 10.1034 21.3287 10.0908 20.626C10.0593 19.2513 9.99642 17.8644 10.1411 16.502C10.399 14.1041 12.4813 12.2055 14.9158 11.9589C17.7843 11.6753 20.3132 13.4877 20.8857 16.2493C20.9611 16.613 20.9863 16.9952 20.9926 17.3712C21.0052 18.3328 20.9989 19.2945 20.9989 20.2561C20.9989 21.3226 20.3195 21.9883 19.2375 21.9945C18.8852 21.9945 18.5329 22.0007 18.1806 21.9883C17.3125 21.9513 16.6457 21.2794 16.6268 20.4349C16.6143 19.8678 16.6143 19.2945 16.6268 18.7274C16.6457 17.8644 17.344 17.1863 18.2247 17.1739C18.6776 17.1678 19.1431 17.1801 19.6212 17.1801Z"
                          fill="var(--font-color1)" />
                      </svg>
                      <svg class="emty_l_hover" width="21" height="22" viewBox="0 0 21 22" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M19.4948 12.1068C17.2112 10.6027 14.8396 10.3315 12.4366 11.589C10.0398 12.8404 8.93264 14.9055 8.90748 17.5685C8.81941 17.5685 8.74392 17.5685 8.66843 17.5685C7.42916 17.5685 6.18989 17.5685 4.95061 17.5685C4.36558 17.5685 4.12024 17.3342 4.12024 16.7548C4.12024 11.4411 4.12024 6.12739 4.12024 0.813697C4.11395 0.234246 4.35299 0 4.94432 0C8.97039 0 12.9964 0 17.0225 0C18.4945 0 19.4948 0.9863 19.4948 2.4226C19.4948 5.57259 19.4948 8.72875 19.4948 11.8787C19.4948 11.9466 19.4948 12.0144 19.4948 12.1068ZM11.8201 6.20136C12.9021 6.20136 13.9841 6.20136 15.0661 6.20136C15.5316 6.20136 15.8399 5.94862 15.8461 5.5541C15.8524 5.16574 15.5442 4.87602 15.0913 4.87602C12.9021 4.86985 10.7129 4.87602 8.52374 4.87602C8.39164 4.87602 8.25324 4.90684 8.14001 4.95616C7.85693 5.08561 7.71224 5.39999 7.78773 5.68972C7.86322 5.99177 8.12114 6.20136 8.45455 6.20136C9.5743 6.20136 10.694 6.20136 11.8201 6.20136ZM11.8201 8.96916C12.3359 8.96916 12.8518 8.96916 13.3739 8.96916C13.808 8.96916 14.1225 8.69177 14.1225 8.30341C14.1225 7.92122 13.8142 7.63766 13.3739 7.63766C12.3296 7.63149 11.2917 7.63149 10.2474 7.63766C9.80076 7.63766 9.51139 7.91506 9.51139 8.30341C9.51139 8.69177 9.81334 8.963 10.2537 8.963C10.7695 8.97533 11.2917 8.96916 11.8201 8.96916Z"
                          fill="var(--text-color)" />
                        <path
                          d="M3.04471 0.0124731C3.02584 0.406993 2.98809 0.789184 2.98809 1.17138C2.9818 6.27548 2.9818 11.3857 2.98809 16.4898C2.98809 16.8412 3.03213 17.1926 3.051 17.5563C2.99438 17.5624 2.89373 17.5748 2.79308 17.5809C1.90609 17.6549 1.39025 18.407 1.36509 19.1837C1.33992 19.9172 1.98787 20.5707 2.7805 20.657C2.90631 20.6693 3.03213 20.6755 3.15794 20.6755C4.98854 20.6755 6.81914 20.6755 8.64345 20.6755C8.73782 20.6755 8.83218 20.6755 8.87621 20.6755C9.05235 21.1316 9.21591 21.557 9.37947 21.9885C9.33543 21.9885 9.26624 21.9946 9.19704 21.9946C7.13368 21.9946 5.07032 22.0008 3.00068 21.9946C1.51606 21.9885 0.333408 21.0576 0.0566165 19.6645C-0.00629073 19.3563 0.00629072 19.0357 0.00629072 18.7213C0.00629072 13.4816 0.00629072 8.24808 0 3.00836C0 1.86179 0.478095 0.974115 1.48461 0.376171C1.98158 0.0987743 2.51629 -0.0306776 3.04471 0.0124731Z"
                          fill="var(--text-color)" />
                        <path
                          d="M19.6212 17.1801C19.6149 14.9363 17.5956 13.1178 15.2303 13.2719C12.6826 13.4383 11.3678 15.6452 11.4433 17.1739C11.8711 17.1739 12.3051 17.1739 12.7329 17.1739C13.7205 17.1801 14.4062 17.8397 14.4251 18.8013C14.4314 19.3253 14.4377 19.8493 14.4251 20.3733C14.4062 21.3164 13.7331 21.976 12.7706 21.9945C12.3554 22.0007 11.9403 22.0068 11.5251 21.9822C10.8016 21.939 10.1034 21.3287 10.0908 20.626C10.0593 19.2513 9.99642 17.8644 10.1411 16.502C10.399 14.1041 12.4813 12.2055 14.9158 11.9589C17.7843 11.6753 20.3132 13.4877 20.8857 16.2493C20.9611 16.613 20.9863 16.9952 20.9926 17.3712C21.0052 18.3328 20.9989 19.2945 20.9989 20.2561C20.9989 21.3226 20.3195 21.9883 19.2375 21.9945C18.8852 21.9945 18.5329 22.0007 18.1806 21.9883C17.3125 21.9513 16.6457 21.2794 16.6268 20.4349C16.6143 19.8678 16.6143 19.2945 16.6268 18.7274C16.6457 17.8644 17.344 17.1863 18.2247 17.1739C18.6776 17.1678 19.1431 17.1801 19.6212 17.1801Z"
                          fill="var(--text-color)" />
                      </svg>
                    </div>
                    <p class="show_p_md">
                      слушать
                    </p>
                    <div class="on_hov shrt">
                      слушать
                    </div>
                  </button>
                  <div class="flex items-center cursor-pointer icons_w show_md">
                    <div class="icon-wrap star_w no_h">
                      <svg width="7" height="22" viewBox="0 0 7 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="3.27778" cy="3.27778" r="2.27778" fill="#DEA53D" stroke="#DEA53D" />
                        <circle cx="3.27778" cy="11.2499" r="2.27778" fill="#DEA53D" stroke="#DEA53D" />
                        <circle cx="3.27778" cy="19.2221" r="2.27778" fill="#DEA53D" stroke="#DEA53D" />
                      </svg>
                    </div>
                  </div>
                  <div class="md_chg">
                    <button *ngIf="hasAudio(item)" class="flex items-center icons_w lik_hov d__none" (click)="navigateToAudiobook(item, $event)">
                      <div class="icon-wrap like_w">
                        <svg class="emty_l" width="21" height="22" viewBox="0 0 21 22" fill="none"
                          xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M19.4948 12.1068C17.2112 10.6027 14.8396 10.3315 12.4366 11.589C10.0398 12.8404 8.93264 14.9055 8.90748 17.5685C8.81941 17.5685 8.74392 17.5685 8.66843 17.5685C7.42916 17.5685 6.18989 17.5685 4.95061 17.5685C4.36558 17.5685 4.12024 17.3342 4.12024 16.7548C4.12024 11.4411 4.12024 6.12739 4.12024 0.813697C4.11395 0.234246 4.35299 0 4.94432 0C8.97039 0 12.9964 0 17.0225 0C18.4945 0 19.4948 0.9863 19.4948 2.4226C19.4948 5.57259 19.4948 8.72875 19.4948 11.8787C19.4948 11.9466 19.4948 12.0144 19.4948 12.1068ZM11.8201 6.20136C12.9021 6.20136 13.9841 6.20136 15.0661 6.20136C15.5316 6.20136 15.8399 5.94862 15.8461 5.5541C15.8524 5.16574 15.5442 4.87602 15.0913 4.87602C12.9021 4.86985 10.7129 4.87602 8.52374 4.87602C8.39164 4.87602 8.25324 4.90684 8.14001 4.95616C7.85693 5.08561 7.71224 5.39999 7.78773 5.68972C7.86322 5.99177 8.12114 6.20136 8.45455 6.20136C9.5743 6.20136 10.694 6.20136 11.8201 6.20136ZM11.8201 8.96916C12.3359 8.96916 12.8518 8.96916 13.3739 8.96916C13.808 8.96916 14.1225 8.69177 14.1225 8.30341C14.1225 7.92122 13.8142 7.63766 13.3739 7.63766C12.3296 7.63149 11.2917 7.63149 10.2474 7.63766C9.80076 7.63766 9.51139 7.91506 9.51139 8.30341C9.51139 8.69177 9.81334 8.963 10.2537 8.963C10.7695 8.97533 11.2917 8.96916 11.8201 8.96916Z"
                            fill="var(--font-color1)" />
                          <path
                            d="M3.04471 0.0124731C3.02584 0.406993 2.98809 0.789184 2.98809 1.17138C2.9818 6.27548 2.9818 11.3857 2.98809 16.4898C2.98809 16.8412 3.03213 17.1926 3.051 17.5563C2.99438 17.5624 2.89373 17.5748 2.79308 17.5809C1.90609 17.6549 1.39025 18.407 1.36509 19.1837C1.33992 19.9172 1.98787 20.5707 2.7805 20.657C2.90631 20.6693 3.03213 20.6755 3.15794 20.6755C4.98854 20.6755 6.81914 20.6755 8.64345 20.6755C8.73782 20.6755 8.83218 20.6755 8.87621 20.6755C9.05235 21.1316 9.21591 21.557 9.37947 21.9885C9.33543 21.9885 9.26624 21.9946 9.19704 21.9946C7.13368 21.9946 5.07032 22.0008 3.00068 21.9946C1.51606 21.9885 0.333408 21.0576 0.0566165 19.6645C-0.00629073 19.3563 0.00629072 19.0357 0.00629072 18.7213C0.00629072 13.4816 0.00629072 8.24808 0 3.00836C0 1.86179 0.478095 0.974115 1.48461 0.376171C1.98158 0.0987743 2.51629 -0.0306776 3.04471 0.0124731Z"
                            fill="var(--font-color1)" />
                          <path
                            d="M19.6212 17.1801C19.6149 14.9363 17.5956 13.1178 15.2303 13.2719C12.6826 13.4383 11.3678 15.6452 11.4433 17.1739C11.8711 17.1739 12.3051 17.1739 12.7329 17.1739C13.7205 17.1801 14.4062 17.8397 14.4251 18.8013C14.4314 19.3253 14.4377 19.8493 14.4251 20.3733C14.4062 21.3164 13.7331 21.976 12.7706 21.9945C12.3554 22.0007 11.9403 22.0068 11.5251 21.9822C10.8016 21.939 10.1034 21.3287 10.0908 20.626C10.0593 19.2513 9.99642 17.8644 10.1411 16.502C10.399 14.1041 12.4813 12.2055 14.9158 11.9589C17.7843 11.6753 20.3132 13.4877 20.8857 16.2493C20.9611 16.613 20.9863 16.9952 20.9926 17.3712C21.0052 18.3328 20.9989 19.2945 20.9989 20.2561C20.9989 21.3226 20.3195 21.9883 19.2375 21.9945C18.8852 21.9945 18.5329 22.0007 18.1806 21.9883C17.3125 21.9513 16.6457 21.2794 16.6268 20.4349C16.6143 19.8678 16.6143 19.2945 16.6268 18.7274C16.6457 17.8644 17.344 17.1863 18.2247 17.1739C18.6776 17.1678 19.1431 17.1801 19.6212 17.1801Z"
                            fill="var(--font-color1)" />
                        </svg>
                        <svg class="emty_l_hover" width="21" height="22" viewBox="0 0 21 22" fill="none"
                          xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M19.4948 12.1068C17.2112 10.6027 14.8396 10.3315 12.4366 11.589C10.0398 12.8404 8.93264 14.9055 8.90748 17.5685C8.81941 17.5685 8.74392 17.5685 8.66843 17.5685C7.42916 17.5685 6.18989 17.5685 4.95061 17.5685C4.36558 17.5685 4.12024 17.3342 4.12024 16.7548C4.12024 11.4411 4.12024 6.12739 4.12024 0.813697C4.11395 0.234246 4.35299 0 4.94432 0C8.97039 0 12.9964 0 17.0225 0C18.4945 0 19.4948 0.9863 19.4948 2.4226C19.4948 5.57259 19.4948 8.72875 19.4948 11.8787C19.4948 11.9466 19.4948 12.0144 19.4948 12.1068ZM11.8201 6.20136C12.9021 6.20136 13.9841 6.20136 15.0661 6.20136C15.5316 6.20136 15.8399 5.94862 15.8461 5.5541C15.8524 5.16574 15.5442 4.87602 15.0913 4.87602C12.9021 4.86985 10.7129 4.87602 8.52374 4.87602C8.39164 4.87602 8.25324 4.90684 8.14001 4.95616C7.85693 5.08561 7.71224 5.39999 7.78773 5.68972C7.86322 5.99177 8.12114 6.20136 8.45455 6.20136C9.5743 6.20136 10.694 6.20136 11.8201 6.20136ZM11.8201 8.96916C12.3359 8.96916 12.8518 8.96916 13.3739 8.96916C13.808 8.96916 14.1225 8.69177 14.1225 8.30341C14.1225 7.92122 13.8142 7.63766 13.3739 7.63766C12.3296 7.63149 11.2917 7.63149 10.2474 7.63766C9.80076 7.63766 9.51139 7.91506 9.51139 8.30341C9.51139 8.69177 9.81334 8.963 10.2537 8.963C10.7695 8.97533 11.2917 8.96916 11.8201 8.96916Z"
                            fill="var(--text-color)" />
                          <path
                            d="M3.04471 0.0124731C3.02584 0.406993 2.98809 0.789184 2.98809 1.17138C2.9818 6.27548 2.9818 11.3857 2.98809 16.4898C2.98809 16.8412 3.03213 17.1926 3.051 17.5563C2.99438 17.5624 2.89373 17.5748 2.79308 17.5809C1.90609 17.6549 1.39025 18.407 1.36509 19.1837C1.33992 19.9172 1.98787 20.5707 2.7805 20.657C2.90631 20.6693 3.03213 20.6755 3.15794 20.6755C4.98854 20.6755 6.81914 20.6755 8.64345 20.6755C8.73782 20.6755 8.83218 20.6755 8.87621 20.6755C9.05235 21.1316 9.21591 21.557 9.37947 21.9885C9.33543 21.9885 9.26624 21.9946 9.19704 21.9946C7.13368 21.9946 5.07032 22.0008 3.00068 21.9946C1.51606 21.9885 0.333408 21.0576 0.0566165 19.6645C-0.00629073 19.3563 0.00629072 19.0357 0.00629072 18.7213C0.00629072 13.4816 0.00629072 8.24808 0 3.00836C0 1.86179 0.478095 0.974115 1.48461 0.376171C1.98158 0.0987743 2.51629 -0.0306776 3.04471 0.0124731Z"
                            fill="var(--text-color)" />
                          <path
                            d="M19.6212 17.1801C19.6149 14.9363 17.5956 13.1178 15.2303 13.2719C12.6826 13.4383 11.3678 15.6452 11.4433 17.1739C11.8711 17.1739 12.3051 17.1739 12.7329 17.1739C13.7205 17.1801 14.4062 17.8397 14.4251 18.8013C14.4314 19.3253 14.4377 19.8493 14.4251 20.3733C14.4062 21.3164 13.7331 21.976 12.7706 21.9945C12.3554 22.0007 11.9403 22.0068 11.5251 21.9822C10.8016 21.939 10.1034 21.3287 10.0908 20.626C10.0593 19.2513 9.99642 17.8644 10.1411 16.502C10.399 14.1041 12.4813 12.2055 14.9158 11.9589C17.7843 11.6753 20.3132 13.4877 20.8857 16.2493C20.9611 16.613 20.9863 16.9952 20.9926 17.3712C21.0052 18.3328 20.9989 19.2945 20.9989 20.2561C20.9989 21.3226 20.3195 21.9883 19.2375 21.9945C18.8852 21.9945 18.5329 22.0007 18.1806 21.9883C17.3125 21.9513 16.6457 21.2794 16.6268 20.4349C16.6143 19.8678 16.6143 19.2945 16.6268 18.7274C16.6457 17.8644 17.344 17.1863 18.2247 17.1739C18.6776 17.1678 19.1431 17.1801 19.6212 17.1801Z"
                            fill="var(--text-color)" />
                        </svg>
                      </div>
                      <p class="show_p_md">
                        слушать
                      </p>
                      <div class="on_hov shrt">
                        слушать
                      </div>
                    </button>
                    <div class="flex items-center cursor-pointer icons_w fav_hov"
                      [ngClass]="{'in-favourites': getLangElement(item).inFavourites}"
                      (click)="favorites(getLangElement(item))">
                      <div class="icon-wrap star_w">
                        <svg class="emty_f" width="25" height="24" viewBox="0 0 25 24" fill="none"
                          xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M5.96868 23.9618C5.94253 23.9618 5.91638 23.9618 5.89022 23.9618C5.36715 23.9356 4.89638 23.6999 4.53023 23.3333C4.16408 22.9143 3.98101 22.3906 4.03332 21.8145C4.05947 21.5789 4.11178 21.317 4.19024 21.0552C4.71331 19.4055 5.41946 17.2844 6.1256 15.1372C4.42562 13.9065 2.72564 12.6758 1.02565 11.4189C0.816424 11.2618 0.162584 10.7904 0.0318159 9.95249C-0.0727985 9.35022 0.0841231 8.77414 0.450273 8.35517C0.842577 7.88383 1.44411 7.62197 2.15026 7.62197C2.90871 7.62197 3.66717 7.62197 4.42562 7.62197H8.55789C8.68866 7.25537 8.79327 6.88878 8.92404 6.52218C9.08096 6.05084 9.21173 5.60568 9.36865 5.13435C9.7348 4.00837 10.0748 2.8562 10.4671 1.73022C10.5979 1.31125 10.8333 0.918472 11.1209 0.604245C11.4871 0.211462 12.0363 -0.0242083 12.5855 0.00197721C13.1348 0.0281628 13.6578 0.290018 14.024 0.735173C14.2332 1.02321 14.4163 1.33744 14.5471 1.70404C15.044 3.24899 15.5409 4.79393 16.0378 6.33888L16.4563 7.62197H17.6593C19.3332 7.62197 21.007 7.62197 22.6808 7.62197C23.047 7.62197 23.387 7.67434 23.7008 7.77909C24.2762 7.98857 24.7208 8.43372 24.9039 9.00981C25.087 9.58589 25.0085 10.1882 24.6685 10.7119C24.4854 10.9999 24.25 11.2356 23.9624 11.4451C22.2624 12.6758 20.5624 13.9327 18.8886 15.1372C19.5424 17.1273 20.1962 19.0912 20.8239 21.0813C21.0593 21.7884 21.0332 22.3906 20.7455 22.9405C20.327 23.7785 19.3593 24.1974 18.4701 23.9094C18.1301 23.8046 17.8163 23.6475 17.5286 23.438C16.1424 22.443 14.7563 21.4218 13.344 20.4005L12.5071 19.7983L11.5132 20.5314C10.1794 21.5003 8.84558 22.4954 7.51174 23.4642C6.96252 23.7785 6.4656 23.9618 5.96868 23.9618ZM3.45794 9.14073C3.01333 9.14073 2.59487 9.14073 2.15026 9.14073C1.91487 9.14073 1.7318 9.21929 1.65334 9.32403C1.57488 9.40259 1.54872 9.53352 1.57488 9.66445C1.60103 9.76919 1.65334 9.90012 1.94103 10.1096C3.71947 11.3927 5.47176 12.702 7.25021 13.9851C7.69482 14.3255 7.85174 14.7968 7.69482 15.3205C6.96252 17.5463 6.23022 19.7983 5.68099 21.5003C5.62869 21.6312 5.60253 21.7884 5.60253 21.8931C5.60253 22.0502 5.62869 22.155 5.70715 22.2597C5.75945 22.3383 5.86407 22.3644 5.94253 22.3644C6.0733 22.3644 6.23022 22.3383 6.54406 22.1026C7.87789 21.1337 9.21173 20.1387 10.5456 19.1698L12.4809 17.7558L14.2594 19.0389C15.6455 20.0601 17.0317 21.0552 18.4178 22.0764C18.5747 22.1811 18.7316 22.2597 18.9147 22.3121C19.0716 22.3644 19.2286 22.2859 19.307 22.1288C19.3593 22.024 19.4116 21.8407 19.2809 21.4741C18.6009 19.4055 17.9209 17.3368 17.2409 15.2682C17.0578 14.7183 17.2147 14.2731 17.6593 13.9327C19.4116 12.6758 21.1901 11.3665 22.9424 10.0834C23.0731 9.97867 23.1777 9.87393 23.2824 9.743C23.3608 9.63826 23.387 9.50733 23.3347 9.40259C23.3085 9.29785 23.2039 9.21929 23.0993 9.16692C22.9685 9.11455 22.7854 9.08836 22.6024 9.08836C20.9285 9.08836 19.2547 9.08836 17.5809 9.08836H15.2794L15.0963 8.59084C15.0701 8.51228 15.044 8.45991 15.0178 8.40754L14.4948 6.78404C13.9978 5.23909 13.5009 3.69414 13.004 2.14919C12.9517 1.96589 12.8471 1.78259 12.7425 1.65167C12.664 1.54692 12.5332 1.52074 12.4809 1.52074C12.4286 1.52074 12.324 1.52074 12.2455 1.62548C12.1148 1.75641 12.0102 1.96589 11.9317 2.17538C11.5656 3.30136 11.1994 4.45352 10.8333 5.55331C10.6763 6.02465 10.5456 6.46981 10.3886 6.94115C10.2579 7.30775 10.1533 7.70053 10.0225 8.06713L9.68249 9.14073H4.42562C4.11178 9.14073 3.77178 9.14073 3.45794 9.14073Z"
                            fill="var(--font-color1)" />
                        </svg>
                        <svg class="emty_f_hover" width="25" height="24" viewBox="0 0 25 24" fill="none"
                          xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M5.96868 23.9618C5.94253 23.9618 5.91638 23.9618 5.89022 23.9618C5.36715 23.9356 4.89638 23.6999 4.53023 23.3333C4.16408 22.9143 3.98101 22.3906 4.03332 21.8145C4.05947 21.5789 4.11178 21.317 4.19024 21.0552C4.71331 19.4055 5.41946 17.2844 6.1256 15.1372C4.42562 13.9065 2.72564 12.6758 1.02565 11.4189C0.816424 11.2618 0.162584 10.7904 0.0318159 9.95249C-0.0727985 9.35022 0.0841231 8.77414 0.450273 8.35517C0.842577 7.88383 1.44411 7.62197 2.15026 7.62197C2.90871 7.62197 3.66717 7.62197 4.42562 7.62197H8.55789C8.68866 7.25537 8.79327 6.88878 8.92404 6.52218C9.08096 6.05084 9.21173 5.60568 9.36865 5.13435C9.7348 4.00837 10.0748 2.8562 10.4671 1.73022C10.5979 1.31125 10.8333 0.918472 11.1209 0.604245C11.4871 0.211462 12.0363 -0.0242083 12.5855 0.00197721C13.1348 0.0281628 13.6578 0.290018 14.024 0.735173C14.2332 1.02321 14.4163 1.33744 14.5471 1.70404C15.044 3.24899 15.5409 4.79393 16.0378 6.33888L16.4563 7.62197H17.6593C19.3332 7.62197 21.007 7.62197 22.6808 7.62197C23.047 7.62197 23.387 7.67434 23.7008 7.77909C24.2762 7.98857 24.7208 8.43372 24.9039 9.00981C25.087 9.58589 25.0085 10.1882 24.6685 10.7119C24.4854 10.9999 24.25 11.2356 23.9624 11.4451C22.2624 12.6758 20.5624 13.9327 18.8886 15.1372C19.5424 17.1273 20.1962 19.0912 20.8239 21.0813C21.0593 21.7884 21.0332 22.3906 20.7455 22.9405C20.327 23.7785 19.3593 24.1974 18.4701 23.9094C18.1301 23.8046 17.8163 23.6475 17.5286 23.438C16.1424 22.443 14.7563 21.4218 13.344 20.4005L12.5071 19.7983L11.5132 20.5314C10.1794 21.5003 8.84558 22.4954 7.51174 23.4642C6.96252 23.7785 6.4656 23.9618 5.96868 23.9618ZM3.45794 9.14073C3.01333 9.14073 2.59487 9.14073 2.15026 9.14073C1.91487 9.14073 1.7318 9.21929 1.65334 9.32403C1.57488 9.40259 1.54872 9.53352 1.57488 9.66445C1.60103 9.76919 1.65334 9.90012 1.94103 10.1096C3.71947 11.3927 5.47176 12.702 7.25021 13.9851C7.69482 14.3255 7.85174 14.7968 7.69482 15.3205C6.96252 17.5463 6.23022 19.7983 5.68099 21.5003C5.62869 21.6312 5.60253 21.7884 5.60253 21.8931C5.60253 22.0502 5.62869 22.155 5.70715 22.2597C5.75945 22.3383 5.86407 22.3644 5.94253 22.3644C6.0733 22.3644 6.23022 22.3383 6.54406 22.1026C7.87789 21.1337 9.21173 20.1387 10.5456 19.1698L12.4809 17.7558L14.2594 19.0389C15.6455 20.0601 17.0317 21.0552 18.4178 22.0764C18.5747 22.1811 18.7316 22.2597 18.9147 22.3121C19.0716 22.3644 19.2286 22.2859 19.307 22.1288C19.3593 22.024 19.4116 21.8407 19.2809 21.4741C18.6009 19.4055 17.9209 17.3368 17.2409 15.2682C17.0578 14.7183 17.2147 14.2731 17.6593 13.9327C19.4116 12.6758 21.1901 11.3665 22.9424 10.0834C23.0731 9.97867 23.1777 9.87393 23.2824 9.743C23.3608 9.63826 23.387 9.50733 23.3347 9.40259C23.3085 9.29785 23.2039 9.21929 23.0993 9.16692C22.9685 9.11455 22.7854 9.08836 22.6024 9.08836C20.9285 9.08836 19.2547 9.08836 17.5809 9.08836H15.2794L15.0963 8.59084C15.0701 8.51228 15.044 8.45991 15.0178 8.40754L14.4948 6.78404C13.9978 5.23909 13.5009 3.69414 13.004 2.14919C12.9517 1.96589 12.8471 1.78259 12.7425 1.65167C12.664 1.54692 12.5332 1.52074 12.4809 1.52074C12.4286 1.52074 12.324 1.52074 12.2455 1.62548C12.1148 1.75641 12.0102 1.96589 11.9317 2.17538C11.5656 3.30136 11.1994 4.45352 10.8333 5.55331C10.6763 6.02465 10.5456 6.46981 10.3886 6.94115C10.2579 7.30775 10.1533 7.70053 10.0225 8.06713L9.68249 9.14073H4.42562C4.11178 9.14073 3.77178 9.14073 3.45794 9.14073Z"
                            fill="var(--text-color)" />
                        </svg>
                      </div>
                      <p class="show_p_md">
                        добавить в избранное
                      </p>
                      <div class="on_hov">
                        добавить в избранное
                      </div>
                    </div>
                    <div class="flex items-center cursor-pointer icons_w shr_hov" *ngIf="profileService.profile"
                      (click)="share(item)">
                      <div class="icon-wrap share_w">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M15.9238 7.01009C13.5066 8.21941 11.0965 9.42157 8.66504 10.638C8.9368 11.5325 8.94395 12.427 8.66504 13.3357C11.0822 14.5451 13.4923 15.7544 15.8737 16.9422C16.2456 16.5988 16.5745 16.2267 16.9607 15.9476C19.6353 13.9941 23.4542 15.6113 23.9334 18.8957C24.3481 21.7509 22.0168 24.2268 19.1347 23.9835C17.0894 23.8117 15.3731 22.1516 15.1514 20.1051C15.1085 19.6829 15.1156 19.2464 15.1729 18.8313C15.2086 18.588 15.1442 18.5093 14.9512 18.4163C12.8772 17.3859 10.8105 16.3483 8.73656 15.3107C8.50056 15.1891 8.25741 15.0817 8.02141 14.9458C7.87838 14.8671 7.79972 14.8885 7.6853 15.0102C6.76276 15.9834 5.63283 16.4556 4.28836 16.4413C2.21445 16.4127 0.326467 14.7454 0.0475605 12.6846C-0.288557 10.2588 1.18464 8.12638 3.58752 7.63979C5.16084 7.32494 6.52676 7.79722 7.65669 8.94213C7.80687 9.09241 7.89984 9.10672 8.07862 9.01369C10.3742 7.85447 12.677 6.70955 14.9798 5.56464C15.1585 5.47877 15.2158 5.40005 15.1729 5.18538C14.7295 2.75959 16.4458 0.39105 18.8773 0.047575C21.4232 -0.317367 23.7117 1.45725 23.9763 3.99753C24.2266 6.37323 22.4673 8.57004 20.093 8.8348C18.4196 9.028 17.0751 8.41977 16.0239 7.12458C15.9881 7.09596 15.9595 7.06018 15.9238 7.01009ZM7.14179 11.9976C7.14179 10.5021 5.91174 9.26414 4.42424 9.2713C2.94389 9.27845 1.721 10.4949 1.70669 11.9762C1.69239 13.4574 2.92244 14.7025 4.41709 14.7168C5.90459 14.7311 7.14179 13.5003 7.14179 11.9976ZM16.832 19.5541C16.8248 21.0496 18.0549 22.2876 19.5424 22.2804C21.0227 22.2804 22.2671 21.0353 22.2671 19.5541C22.2599 18.0728 21.0513 16.8635 19.5567 16.8492C18.062 16.8277 16.8391 18.0442 16.832 19.5541ZM19.5424 7.14605C21.037 7.14605 22.2671 5.91527 22.2671 4.42688C22.2599 2.93849 21.0156 1.70055 19.5352 1.7077C18.0477 1.71486 16.8463 2.93133 16.832 4.41972C16.8248 5.92242 18.0406 7.14605 19.5424 7.14605Z"
                            fill="var(--font-color1)" />
                        </svg>
                      </div>
                      <p class="show_p_md">
                        поделиться
                      </p>
                      <div class="on_hov">
                        поделиться
                      </div>
                    </div>
                    <button class="flex items-center icons_w lik_hov"
                      [ngClass]="{'is-liked': getLangElement(item).liked}" (click)="like(getLangElement(item))">
                      <div class="icon-wrap like_w">
                        <svg class="emty_l" width="24" height="22" viewBox="0 0 24 22" fill="none"
                          xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                            fill="var(--font-color1)" />
                        </svg>
                        <svg class="emty_l_hover" width="24" height="22" viewBox="0 0 24 22" fill="none"
                          xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                            fill="var(--text-color)" />
                        </svg>
                      </div>
                      <p class="show_p_md">
                        мне нравится
                      </p>
                      <span class="ml-2 text-color default_">
                        {{getLangElement(item).likes}}
                      </span>
                      <div class="on_hov">
                        мне нравится
                      </div>
                    </button>
                  </div>
                </div>
              </div>
              <!-- <div class="flex bs_wrapper_">
                <p [routerLink]="['/ru/library/' + item.code]" class="btn_item_wrapper">
                  <span class="btn_item_">Подробнее</span>
                </p>
              </div> -->
            </div>
            <!-- <div class="flex bs_wrapper_ vis_md">
              <p [routerLink]="['/ru/library/' + item.code]" class="btn_item_wrapper">
                <span class="btn_item_">Подробнее</span>
              </p>
            </div> -->
            <div class="invis_part">
              <div class="flex">
                <div class="flex flex-col icons_w b_p">
                  <div class="tr_descr">{{getLangElement(item).annotation}}</div>
                  <!-- <span class="mt-u_ text-color">Длительность: {{Math.ceil(track.duration / 60)}} мин.</span> -->
                </div>
              </div>
            </div>
          </div>
          }
        </div>
      </div>

      <div class="buttn_catg" *ngIf="filters.page < libraryService.totalPages && data.length">
        <button class="load-more-button" (click)="nextPage()" [disabled]="false">
          <span *ngIf="true">Загрузить еще</span>
          <span *ngIf="false">Загрузка...</span>
        </button>
      </div>

    </div>
  </div>
</div>
